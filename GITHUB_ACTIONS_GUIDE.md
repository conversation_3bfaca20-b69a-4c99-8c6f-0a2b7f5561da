# ThoughtEcho GitHub Actions 编译指南

## 🚀 快速开始

你的开源项目已经配置好了完整的GitHub Actions工作流，支持自动编译Windows应用和创建发布包。

## 📋 工作流概览

### 当前配置
- **工作流文件**: `.github/workflows/build-windows.yml`
- **运行环境**: Windows Server 2022
- **Flutter版本**: 3.29.2 (可配置)
- **输出格式**: ZIP压缩包 + MSIX安装包

## 🔧 使用方法

### 1. 手动触发编译

1. 访问GitHub仓库 → Actions → Build Windows App
2. 点击 **Run workflow** 按钮
3. 配置参数：
   - **应用版本号**: 如 `1.0.0`
   - **Flutter版本**: 可选 3.29.2, 3.27.0, latest
   - **构建类型**: release 或 profile
4. 点击 **Run workflow** 开始编译

### 2. 自动触发配置

#### 推荐配置：推送标签时自动发布

编辑 `.github/workflows/build-windows.yml`，在 `on:` 部分添加：

```yaml
on:
  push:
    tags:
      - 'v*'  # 推送v开头的标签时自动触发
  workflow_dispatch:
    # ... 保持现有配置
```

#### 推送主分支时自动构建

```yaml
on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:
    # ... 保持现有配置
```

### 3. 创建Release发布

#### 方法1：使用GitHub CLI

```bash
# 创建标签
git tag v1.0.0
git push origin v1.0.0

# 如果使用自动触发，GitHub Actions会自动构建
# 然后在Actions页面下载构建产物
```

#### 方法2：手动创建Release

1. 在GitHub仓库 → Releases → Draft new release
2. 创建标签如 `v1.0.0`
3. 填写Release说明
4. 等待GitHub Actions构建完成
5. 在Actions页面下载构建产物
6. 上传到Release附件

## 📁 输出文件

构建完成后，你可以在Actions页面下载：

- **ZIP发布包**: `thoughtecho-windows-x64-v版本号-release.zip`
  - 包含完整应用文件夹
  - 附带启动脚本和版本信息
  - 无需安装，解压即用

- **MSIX安装包**: `thoughtecho-windows-x64-v版本号-release.msix`
  - Windows原生安装格式
  - 支持自动更新
  - 支持自定义安装路径

## ⚙️ 配置文件说明

### 关键文件

1. **msix_config.yaml**: MSIX安装包配置
2. **pubspec.yaml**: Flutter应用配置
3. **build-windows.yml**: GitHub Actions工作流

### 版本号管理

工作流会自动：
- 读取 `pubspec.yaml` 中的版本
- 添加构建号（GitHub run number）
- 更新到最终发布包

## 🚀 开源项目最佳实践

### 1. 徽章配置

在README.md添加构建状态徽章：

```markdown
![Build Windows](https://github.com/你的用户名/ThoughtEcho/workflows/Build%20Windows%20App/badge.svg)
```

### 2. 发布模板

创建 `.github/release.yml`：

```yaml
changelog:
  categories:
    - title: 🚀 新功能
      labels:
        - enhancement
    - title: 🐛 Bug修复
      labels:
        - bug
    - title: 📚 文档更新
      labels:
        - documentation
```

### 3. 权限配置

确保仓库设置：
- Settings → Actions → General → Workflow permissions
- 选择 **Read and write permissions**
- 勾选 **Allow GitHub Actions to create and approve pull requests**

## 🔄 更新Flutter版本

修改工作流中的默认版本：

```yaml
- name: Setup Flutter
  uses: subosito/flutter-action@v2
  with:
    flutter-version: ${{ github.event.inputs.flutter_version || '新版本号' }}
```

## 📋 故障排除

### 常见问题

1. **构建超时**: 增加超时时间
   ```yaml
   jobs:
     build-windows:
       timeout-minutes: 60
   ```

2. **依赖下载失败**: 添加缓存
   ```yaml
   - name: Cache Flutter dependencies
     uses: actions/cache@v3
     with:
       path: ~/.pub-cache
       key: ${{ runner.os }}-pub-${{ hashFiles('**/pubspec.lock') }}
   ```

3. **证书问题**: 开源项目可以跳过代码签名
   ```yaml
   # 在msix_config.yaml中
   certificate_path: null
   certificate_password: null
   ```

## 🎯 下一步

1. 测试手动触发构建
2. 配置自动发布
3. 添加更多平台支持（macOS/Linux）
4. 设置自动更新检查

## 📞 支持

遇到问题请：
1. 查看Actions日志
2. 检查工作流配置
3. 创建Issue寻求帮助