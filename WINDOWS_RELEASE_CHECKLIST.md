# Windows正式版发布检查清单

## ✅ 已完成配置

### 🔧 构建配置
- [x] **GitHub Actions工作流** - 自动构建Windows应用和MSIX安装包
- [x] **Flutter版本管理** - 支持3.29.2等指定版本构建
- [x] **版本号自动更新** - 基于GitHub输入参数动态更新版本
- [x] **双重构建产物** - ZIP压缩包 + MSIX安装包

### 🎨 应用标识
- [x] **应用图标** - 统一使用 `app_icon.ico` (已确认存在)
- [x] **应用名称** - "ThoughtEcho" 已正确配置
- [x] **发布者信息** - "ThoughtEcho Team" 已设置
- [x] **应用描述** - "一款帮助你记录和分析思想的应用"
- [x] **版本信息** - 1.0.0+1 (可动态更新)

### 📦 MSIX配置
- [x] **安装路径选择** - 用户可自定义安装位置
- [x] **系统权限** - 网络访问权限已配置
- [x] **架构支持** - x64架构支持
- [x] **语言本地化** - 中英文支持
- [x] **图标配置** - 所有图标路径已统一
- [x] **应用别名** - 命令行 `thoughtecho` 可用

### 🏗️ Windows原生配置
- [x] **窗口设置** - 1280x720默认窗口大小
- [x] **应用元数据** - 版本信息、版权、公司信息完整
- [x] **错误处理** - 启动失败友好提示
- [x] **窗口管理** - 前台显示、置顶功能

## ⚠️ 发布前需要确认的项目

### 🔐 代码签名证书
**状态**: ❌ 需要配置
- **当前**: 使用null占位符，需要真实证书
- **建议**: 购买代码签名证书或使用自签名证书
- **文件**: `msix_config.yaml` 中的 `certificate_path` 和 `certificate_password`
- **指南**: 参考 `WINDOWS_SIGNING_GUIDE.md`

### 📋 应用权限审核
**状态**: ⚠️ 需要确认
- **当前**: 仅配置了 `internetClient` 权限
- **建议**: 根据实际需求添加必要权限
- **可选权限**:
  - `privateNetworkClientServer` - 本地网络
  - `picturesLibrary` - 图片访问
  - `documentsLibrary` - 文档访问
  - `microphone` - 麦克风权限

### 🔄 版本号策略
**状态**: ⚠️ 需要确定
- **当前**: 1.0.0+1 (开发版本)
- **建议**: 正式版使用语义化版本号 (如: 1.0.0, 1.1.0等)
- **配置**: 通过GitHub Actions输入参数设置

### 📊 系统兼容性
**状态**: ⚠️ 需要测试
- **当前**: 仅支持x64架构
- **建议**: 测试以下系统版本：
  - Windows 10 (版本 1903+)
  - Windows 11
  - Windows Server 2019/2022
- **架构**: 考虑是否需要x86/arm64支持

### 🎯 应用功能完整性
**状态**: ⚠️ 需要验证
- **数据库**: 确认本地数据存储正常
- **网络**: 验证API调用和网络功能
- **文件**: 测试文件读写和权限
- **性能**: 验证内存使用和响应速度

## 📋 发布前最终检查

### 1. 证书配置 (必需)
```bash
# 测试证书配置
flutter pub run msix:create --certificate-path="your-cert.pfx" --certificate-password="your-password"
```

### 2. 版本号确认
```bash
# 检查当前版本
flutter pub run msix:config-info
```

### 3. 构建测试
```bash
# 本地完整构建测试
flutter clean
flutter pub get
flutter build windows --release
flutter pub run msix:create
```

### 4. 安装测试
```bash
# 测试安装包
# 1. 双击MSIX文件测试安装
# 2. 验证自定义安装路径
# 3. 测试应用启动和功能
# 4. 检查开始菜单和桌面快捷方式
```

### 5. 兼容性测试
```bash
# 在不同Windows版本测试
# - Windows 10 家庭版/专业版
# - Windows 11 家庭版/专业版
# - 不同用户权限 (管理员/标准用户)
```

## 🚀 发布流程

### 阶段1: 证书准备
1. [ ] 获取代码签名证书
2. [ ] 配置GitHub Secrets
3. [ ] 测试证书签名

### 阶段2: 版本准备
1. [ ] 确定正式版本号
2. [ ] 更新CHANGELOG.md
3. [ ] 更新应用描述和元数据

### 阶段3: 构建发布
1. [ ] GitHub Actions触发构建
2. [ ] 下载并测试构建产物
3. [ ] 创建GitHub Release
4. [ ] 发布到Microsoft Store (可选)

## 📋 文件清单

### 必需文件
- [x] `msix_config.yaml` - MSIX配置
- [x] `pubspec.yaml` - Flutter配置
- [x] `windows/runner/resources/app_icon.ico` - 应用图标
- [x] `windows/runner/Runner.rc` - Windows资源文件

### 文档文件
- [x] `WINDOWS_RELEASE_CHECKLIST.md` - 本检查清单
- [x] `MSIX_INSTALL_GUIDE.md` - 安装指南
- [x] `WINDOWS_SIGNING_GUIDE.md` - 签名指南
- [x] `INSTALL_PATH_GUIDE.md` - 安装路径指南

### 工作流文件
- [x] `.github/workflows/build-windows.yml` - 构建工作流
- [x] `msix_config.yaml` - 已配置完成

## 🎯 下一步行动

### 立即需要完成
1. **配置代码签名证书** - 这是发布的最重要步骤
2. **确定正式版本号** - 如 1.0.0 正式版
3. **完整功能测试** - 确保所有功能正常工作

### 可选优化
1. **添加更多权限** - 根据功能需求
2. **支持更多架构** - 如需要x86支持
3. **Microsoft Store发布** - 扩大分发渠道

## 📞 技术支持

如遇到问题：
- 证书配置问题：参考 `WINDOWS_SIGNING_GUIDE.md`
- 构建问题：检查GitHub Actions日志
- 安装问题：查看 `MSIX_INSTALL_GUIDE.md`
- 功能问题：检查应用日志和错误信息

**当前状态**: 🟡 配置完成，等待证书和最终测试