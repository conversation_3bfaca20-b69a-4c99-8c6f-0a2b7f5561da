import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:thoughtecho/services/database_service.dart';
import 'package:thoughtecho/services/ai_analysis_database_service.dart';

void main() {
  late DatabaseService databaseService;
  late AIAnalysisDatabaseService aiAnalysisService;

  setUpAll(() async {
    // 初始化FFI数据库用于测试
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  });

  setUp(() async {
    databaseService = DatabaseService();
    aiAnalysisService = AIAnalysisDatabaseService();
  });

  tearDown(() async {
    // 清理数据库服务
    await databaseService.dispose();
    await aiAnalysisService.closeDatabase();
  });

  group('数据库初始化修复测试', () {
    test('测试主数据库初始化', () async {
      await databaseService.init();
      expect(databaseService.isInitialized, isTrue);
      debugPrint('✓ 主数据库初始化成功');
    });

    test('测试AI分析数据库初始化', () async {
      await aiAnalysisService.init();
      final db = await aiAnalysisService.database;
      expect(db.isOpen, isTrue);
      debugPrint('✓ AI分析数据库初始化成功');
    });

    test('测试day_period索引创建', () async {
      await databaseService.init();
      
      // 检查索引是否存在
      final db = await databaseService.safeDatabase;
      final result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='index' AND name='idx_quotes_day_period'"
      );
      
      // 如果day_period列存在，索引应该被创建
      final columnExists = await _checkColumnExists(db, 'quotes', 'day_period');
      if (columnExists) {
        expect(result.isNotEmpty, isTrue);
        debugPrint('✓ day_period索引创建成功');
      } else {
        debugPrint('✓ day_period列不存在，跳过索引创建');
      }
    });

    test('测试数据库工厂初始化', () async {
      // 验证数据库工厂已正确初始化
      expect(databaseFactory, isNotNull);
      
      // 尝试获取数据库路径
      final path = await databaseFactory.getDatabasesPath();
      expect(path, isNotEmpty);
      debugPrint('✓ 数据库工厂初始化正确，路径: $path');
    });
  });
}

/// 检查列是否存在的辅助方法
Future<bool> _checkColumnExists(Database db, String tableName, String columnName) async {
  try {
    final result = await db.rawQuery("PRAGMA table_info($tableName)");
    for (final row in result) {
      if (row['name'] == columnName) {
        return true;
      }
    }
    return false;
  } catch (e) {
    return false;
  }
}
