# MSIX 完整配置选项指南

## 基础配置

### 应用标识
```yaml
msix_config:
  display_name: "ThoughtEcho"                    # 显示名称
  identity_name: "ThoughtEcho"                   # 唯一标识符
  publisher_display_name: "ThoughtEcho Team"     # 发布者显示名称
  publisher: "CN=ThoughtEcho"                   # 证书发布者
  msix_version: *******                          # 版本号 (主.次.构建.修订)
```

### 应用信息
```yaml
  description: "应用描述"                          # 应用描述
  certificate_path: "path/to/cert.pfx"          # 签名证书路径
  certificate_password: "password"              # 证书密码
  output_name: "ThoughtEcho-Setup.msix"         # 输出文件名
```

## 高级配置选项

### 系统能力 (Capabilities)
```yaml
  capabilities: |
    internetClient                                # 网络访问
    privateNetworkClientServer                    # 本地网络
    picturesLibrary                               # 图片库访问
    videosLibrary                                # 视频库访问
    musicLibrary                                 # 音乐库访问
    documentsLibrary                             # 文档库访问
    removableStorage                             # 可移动存储
    microphone                                   # 麦克风
    webcam                                       # 摄像头
    location                                     # 位置信息
    bluetooth                                    # 蓝牙
    contacts                                     # 联系人
    appointments                                 # 日历
    phoneCall                                    # 电话
    userAccountInformation                       # 用户账户信息
```

### 文件关联
```yaml
  file_extension: |
    .txt                                          # 文本文件
    .md                                           # Markdown文件
    .json                                         # JSON文件
    .xml                                          # XML文件
```

### 协议激活
```yaml
  protocol_activation: |
    thoughtecho                                   # 自定义协议
    te                                            # 简写协议
```

### 应用执行别名
```yaml
  app_execution_alias: "thoughtecho"             # 命令行别名
```

### 启动任务
```yaml
  start_menu_icon: "path/to/icon.ico"           # 开始菜单图标
  tile_icon: "path/to/icon.ico"                 # 磁贴图标
```

## 可视化资产配置

### 磁贴配置
```yaml
  visual_elements:
    display_name: "ThoughtEcho"
    description: "应用描述"
    background_color: "#2E7D32"                  # 背景颜色
    foreground_text: "light"                     # 前景文本 (light/dark)
    show_name_on_square150x150_logo: true        # 显示名称
    
    # 各种尺寸图标
    square150x150_logo: "icons/150x150.png"
    square44x44_logo: "icons/44x44.png"
    square310x310_logo: "icons/310x310.png"
    square71x71_logo: "icons/71x71.png"
    wide310x150_logo: "icons/310x150.png"
    
    # 大图标
    large_tile: "icons/large.png"
    small_tile: "icons/small.png"
```

### 启动画面
```yaml
  splash_screen:
    background_color: "#2E7D32"
    image: "assets/splash.png"
    image_position: "center"                    # center/stretch/tile
```

## 安装设置

### 安装位置
```yaml
  install_location: "auto"                        # auto/custom
  install_scope: "userOrMachine"                # user/machine/userOrMachine
```

### 更新设置
```yaml
  auto_update: true                             # 自动更新
  update_url: "https://example.com/update.xml"  # 更新URL
```

### 依赖项
```yaml
  dependencies: |
    Microsoft.VCLibs.x64.14.00
    Microsoft.NET.Native.Framework.2.2
    Microsoft.NET.Native.Runtime.2.2
```

## 权限配置

### 受限能力
```yaml
  restricted_capabilities: |
    runFullTrust                                 # 完全信任运行
    allowElevation                               # 允许提升权限
    developmentModeNetwork                       # 开发模式网络
```

### 应用扩展
```yaml
  extensions:
    - category: "windows.fileTypeAssociation"
      file_type: ".txt"
      description: "Text Document"
      icon: "icons/txt.ico"
      
    - category: "windows.protocol"
      protocol: "thoughtecho"
      description: "ThoughtEcho Protocol"
```

## 本地化配置

### 多语言支持
```yaml
  languages: |
    zh-CN                                         # 简体中文
    zh-TW                                         # 繁体中文
    en-US                                         # 美国英语
    ja-JP                                         # 日语
    ko-KR                                         # 韩语
```

### 本地化字符串
```yaml
  resources:
    - language: "zh-CN"
      display_name: "思维回响"
      description: "一款帮助你记录和分析思想的应用"
      
    - language: "en-US"
      display_name: "ThoughtEcho"
      description: "An app to help you record and analyze your thoughts"
```

## 架构支持

### 目标架构
```yaml
  architecture: |
    x86                                            # 32位
    x64                                            # 64位
    arm                                            # ARM32
    arm64                                          # ARM64
```

## 调试配置

### 开发模式
```yaml
  debug: true                                    # 调试模式
  enable_debugging: true                         # 启用调试
  allow_development_registration: true         # 允许开发注册
```

### 日志设置
```yaml
  enable_logging: true
  log_file_path: "logs/app.log"
  log_level: "verbose"                           # verbose/info/warning/error
```

## 存储设置

### 本地存储
```yaml
  local_cache: "local"                           # 本地缓存
  roaming_cache: "roaming"                       # 漫游缓存
  temp_cache: "temp"                            # 临时缓存
```

## 网络设置

### 网络隔离
```yaml
  network_isolation: true                        # 网络隔离
  proxy_support: true                          # 代理支持
```

## 示例完整配置

```yaml
# 完整的MSIX配置示例
msix_config:
  # 基础信息
  display_name: "ThoughtEcho"
  identity_name: "ThoughtEcho.笔记应用"
  publisher_display_name: "ThoughtEcho Team"
  publisher: "CN=ThoughtEcho Certificate"
  msix_version: *******
  description: "一款强大的思想记录与分析工具"
  
  # 图标和资产
  logo_path: "windows/runner/resources/icon.ico"
  start_menu_icon: "windows/runner/resources/icon.ico"
  tile_icon: "windows/runner/resources/icon.ico"
  
  # 可视化资产
  visual_elements:
    display_name: "ThoughtEcho"
    description: "智能思想记录与分析"
    background_color: "#2E7D32"
    foreground_text: "light"
    show_name_on_square150x150_logo: true
    square150x150_logo: "assets/icons/150x150.png"
    square44x44_logo: "assets/icons/44x44.png"
    wide310x150_logo: "assets/icons/310x150.png"
    large_tile: "assets/icons/large.png"
    small_tile: "assets/icons/small.png"
  
  # 系统能力
  capabilities: |
    internetClient
    privateNetworkClientServer
    picturesLibrary
    videosLibrary
    documentsLibrary
    removableStorage
    microphone
    webcam
    location
  
  # 文件关联
  file_extension: |
    .txt
    .md
    .json
    .xml
    .csv
  
  # 协议激活
  protocol_activation: |
    thoughtecho
    te
  
  # 安装设置
  install_location: "auto"
  install_scope: "userOrMachine"
  architecture: "x64"
  
  # 签名
  certificate_path: "windows/thoughtecho-cert.pfx"
  certificate_password: "${{ secrets.WINDOWS_CERT_PASSWORD }}"
  
  # 输出
  output_name: "ThoughtEcho-v1.2.3.msix"
  
  # 本地化
  languages: |
    zh-CN
    en-US
    ja-JP
```

## 使用命令行参数

### 构建命令
```bash
# 使用配置文件
flutter pub run msix:create

# 使用命令行参数
flutter pub run msix:create --display-name="MyApp" --publisher="CN=MyCompany"

# 指定证书
flutter pub run msix:create --certificate-path="cert.pfx" --certificate-password="pass"

# 调试模式
flutter pub run msix:create --debug
```

## 验证配置

### 验证工具
```bash
# 验证MSIX包
flutter pub run msix:validate

# 检查证书
flutter pub run msix:check-certificate

# 显示配置信息
flutter pub run msix:config-info
```