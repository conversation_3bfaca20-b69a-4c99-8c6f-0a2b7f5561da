# ThoughtEcho Windows MSIX 安装指南

## 什么是MSIX？
MSIX是微软推出的现代应用打包格式，具有以下优势：
- ✅ 自动安装和卸载
- ✅ 支持自动更新
- ✅ 更好的系统集成
- ✅ 数字签名验证
- ✅ 沙箱安全机制

## 安装方法

### 方法一：直接安装MSIX文件
1. 从GitHub Actions下载 `thoughtecho-windows-release-msix` 构建产物
2. 双击下载的 `.msix` 文件
3. 按照提示完成安装

### 方法二：通过PowerShell安装
```powershell
Add-AppxPackage -Path "ThoughtEcho-Setup.msix"
```

## 系统要求
- Windows 10 版本 1809 或更高版本
- Windows 11 所有版本
- 已启用Microsoft Store服务

## 首次安装注意事项

### 1. 信任开发者
如果是首次安装，系统可能会提示"应用安装程序需要管理员批准"。
- 点击"更多信息"
- 点击"仍然安装"

### 2. 安装位置
MSIX应用默认安装在：
```
C:\Program Files\WindowsApps\
```

### 3. 创建桌面快捷方式
安装完成后，可以在开始菜单中找到"ThoughtEcho"，右键选择"固定到开始屏幕"或"固定到任务栏"。

## 卸载方法

### 方法一：通过设置卸载
1. 打开"设置" > "应用" > "应用和功能"
2. 找到"ThoughtEcho"
3. 点击"卸载"

### 方法二：通过PowerShell卸载
```powershell
Get-AppxPackage *thoughtecho* | Remove-AppxPackage
```

## 常见问题

### Q: 安装时提示"无法验证应用包"
A: 这是因为MSIX文件使用了自签名证书。解决方法：
1. 以管理员身份运行PowerShell
2. 执行：`Add-AppxPackage -Path "ThoughtEcho-Setup.msix" -AllowUnsigned`

### Q: 安装后找不到应用
A: MSIX安装的应用在开始菜单中，搜索"ThoughtEcho"即可找到。

### Q: 如何更新到新版本
A: MSIX支持自动更新，新版本发布后会自动提示更新。也可以手动下载新版本重新安装。

### Q: 与ZIP版本有什么区别？
- **MSIX版本**：系统集成更好，支持自动更新，卸载干净
- **ZIP版本**：绿色便携，无需安装，适合开发测试

## 开发说明

### 证书配置
当前MSIX使用自签名证书，正式发布时需要：
1. 获取有效的代码签名证书
2. 在`msix_config.yaml`中配置：
   ```yaml
   certificate_path: "path/to/certificate.pfx"
   certificate_password: "your-password"
   ```

### 自定义配置
编辑`msix_config.yaml`文件可以自定义：
- 应用名称和描述
- 版本号
- 图标路径
- 所需权限
- 安装设置

## 技术支持
如有问题，请在GitHub Issues中提交问题描述。