# Windows 应用签名证书配置指南

## 签名证书类型

Windows 应用需要代码签名证书，与安卓的签名证书完全不同，不能共用：

### 证书类型对比
| 类型 | 安卓 | Windows |
|------|------|---------|
| 格式 | .keystore / .jks | .pfx / .cer |
| 用途 | APK签名 | EXE/MSIX签名 |
| 有效期 | 25年+ | 1-3年 |
| 发布要求 | 所有应用 | 商店应用必需 |

## 证书获取方式

### 1. 正式发布证书（推荐）
**从证书颁发机构(CA)购买**
- **推荐供应商**: 
  - DigiCert Code Signing ($400-600/年)
  - Sectigo Code Signing ($200-400/年)
  - Certum Code Signing ($100-300/年)

- **验证要求**:
  - 企业: 营业执照 + 法人身份证
  - 个人: 身份证 + 地址证明

### 2. 测试证书（开发用）
**免费创建自签名证书**

#### PowerShell 创建方法
```powershell
# 创建新的自签名证书
New-SelfSignedCertificate -Type CodeSigningCert `
  -Subject "CN=ThoughtEcho" `
  -KeyAlgorithm RSA -KeyLength 2048 `
  -CertStoreLocation "Cert:\CurrentUser\My" `
  -NotAfter (Get-Date).AddYears(3)

# 导出为PFX文件（记住Thumbprint）
$cert = Get-ChildItem Cert:\CurrentUser\My | Where-Object {$_.Subject -like "*ThoughtEcho*"}
Export-PfxCertificate -Cert $cert -FilePath "thoughtecho-cert.pfx" -Password (ConvertTo-SecureString -String "YourPassword123" -AsPlainText -Force)
```

#### 使用OpenSSL创建
```bash
# 生成私钥
openssl genrsa -out private.key 2048

# 创建证书请求
openssl req -new -key private.key -out cert.csr -subj "/CN=ThoughtEcho"

# 自签名证书
openssl x509 -req -in cert.csr -signkey private.key -out certificate.crt -days 1095

# 转换为PFX
openssl pkcs12 -export -out thoughtecho-cert.pfx -inkey private.key -in certificate.crt
```

## 证书配置步骤

### 1. 上传证书到GitHub Secrets
1. 进入 GitHub 仓库 → Settings → Secrets and variables → Actions
2. 添加以下secrets：
   - `WINDOWS_CERTIFICATE_PFX`: PFX文件的base64编码内容
   - `WINDOWS_CERTIFICATE_PASSWORD`: PFX文件密码

#### 转换PFX为base64
```powershell
# PowerShell
[Convert]::ToBase64String([IO.File]::ReadAllBytes("thoughtecho-cert.pfx")) | clip

# macOS/Linux
base64 -w 0 thoughtecho-cert.pfx | pbcopy
```

### 2. 修改工作流配置

#### 更新 msix_config.yaml
```yaml
# 在现有配置基础上添加签名信息
certificate_path: "windows/thoughtecho-cert.pfx"
certificate_password: "${{ secrets.WINDOWS_CERTIFICATE_PASSWORD }}"

# 或者使用GitHub Secrets中的证书
signing_options:
  certificate_path: "${{ github.workspace }}/windows/thoughtecho-cert.pfx"
  certificate_password: "${{ secrets.WINDOWS_CERTIFICATE_PASSWORD }}"
```

#### 更新 GitHub Actions 工作流
修改 `.github/workflows/build-windows.yml`：

```yaml
# 在 Build MSIX installer 步骤前添加证书准备
- name: Setup signing certificate
  run: |
    # 从secrets创建证书文件
    $certBytes = [System.Convert]::FromBase64String("${{ secrets.WINDOWS_CERTIFICATE_PFX }}")
    [System.IO.File]::WriteAllBytes("windows/thoughtecho-cert.pfx", $certBytes)

# 确保 MSIX 构建使用证书
- name: Build signed MSIX installer
  run: |
    flutter pub run msix:create --certificate-path="windows/thoughtecho-cert.pfx" --certificate-password="${{ secrets.WINDOWS_CERTIFICATE_PASSWORD }}"
```

## 证书信任配置

### 自签名证书信任
用户安装前需要信任自签名证书：

1. **管理员权限安装证书**
   ```powershell
   # 导入证书到受信任的根证书颁发机构
   Import-PfxCertificate -FilePath "thoughtecho-cert.pfx" -CertStoreLocation "Cert:\LocalMachine\Root" -Password (ConvertTo-SecureString -String "YourPassword123" -AsPlainText -Force)
   ```

2. **用户手动安装**
   - 双击 .cer 文件
   - 选择 "本地计算机"
   - 存储位置: "受信任的根证书颁发机构"

### 正式发布证书
使用CA颁发的证书时，用户无需额外操作，Windows会自动验证。

## 证书续期

### 正式发布证书
- 提前30-60天续期
- 重新上传到GitHub Secrets
- 更新所有引用证书的工作流

### 自签名证书
- 重新生成证书
- 重新配置工作流
- 通知用户更新信任证书

## 常见问题

### Q: 可以和安卓证书共用吗？
**A: 不能**。格式、用途、验证机制完全不同，必须分别配置。

### Q: 自签名证书有什么限制？
**A**: 
- 用户需要手动信任证书
- 某些企业环境可能阻止安装
- SmartScreen可能显示警告

### Q: 证书密码如何安全存储？
**A**: 使用GitHub Secrets，不要硬编码在配置文件或代码中。

### Q: 证书有效期多久？
**A**: 
- 商业证书: 1-3年
- 自签名证书: 可自定义，建议3年

## 下一步操作

1. 选择证书获取方式（商业/自签名）
2. 创建或购买证书
3. 配置GitHub Secrets
4. 更新工作流文件
5. 测试构建和签名流程

需要我帮你配置具体的工作流文件吗？