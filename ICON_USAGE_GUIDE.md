# 图标配置使用指南

## 图标文件位置
- **Windows应用图标**: `windows/runner/resources/icon.ico`（你提供的图标）
- **MSIX安装包图标**: `windows/runner/resources/icon.ico`
- **Windows原生应用图标**: `windows/runner/resources/app_icon.ico`（已自动复制）

## 图标使用说明

### ✅ 已完成配置

1. **MSIX安装包图标**
   - 已更新 `msix_config.yaml` 使用 `windows/runner/resources/icon.ico`
   - 所有MSIX相关的图标路径都已指向你的图标文件

2. **Windows原生应用图标**
   - 已用你的 `icon.ico` 替换原有的 `app_icon.ico`
   - 应用窗口图标、任务栏图标、开始菜单图标都会使用你的图标

3. **应用元数据**
   - 已更新 `windows/runner/Runner.rc` 中的产品信息
   - 应用名称、描述、版权信息都已本地化

### 📋 图标要求

- **格式**: ICO (Windows图标格式)
- **推荐尺寸**: 256x256像素（支持多种尺寸）
- **最小尺寸**: 至少包含 16x16, 32x32, 48x48, 256x256
- **颜色**: 支持透明背景

### 🔍 验证图标效果

构建完成后，检查以下位置的图标显示：

1. **安装包图标**: MSIX文件属性中的图标
2. **开始菜单**: 应用磁贴和列表图标
3. **任务栏**: 应用运行时任务栏图标
4. **窗口标题栏**: 应用窗口左上角图标
5. **添加/删除程序**: 控制面板中的图标

### 🚀 下一步操作

图标已配置完成，无需额外操作。下次构建时：

1. GitHub Actions会自动使用你的图标
2. 生成的MSIX安装包会显示你的应用图标
3. 安装后的应用在所有Windows界面都会显示你的图标

### 💡 提示

如果需要更新图标，只需替换 `windows/runner/resources/icon.ico` 文件，然后重新构建即可。