# 安装路径选择功能指南

## ✅ 已完成配置

### 图标统一
- 所有图标路径已统一为：`windows/runner/resources/app_icon.ico`
- 应用图标、开始菜单图标、磁贴图标均使用同一图标文件

### 安装路径选择
- 已将 `install_location` 从 `"auto"` 改为 `"custom"`
- 用户现在可以在安装时选择自定义安装路径

## 🎯 用户安装体验

### 安装时界面变化
1. **之前**：自动安装到默认路径（通常是 `C:\Program Files\ThoughtEcho`）
2. **现在**：安装向导会弹出路径选择对话框

### 可选安装位置
- **系统盘**：`C:\Program Files\ThoughtEcho`（需要管理员权限）
- **用户目录**：`C:\Users\<USER>\AppData\Local\ThoughtEcho`（无需管理员权限）
- **自定义路径**：用户可自由选择任意有效路径

### 权限要求
- **系统级安装**：需要管理员权限，所有用户可用
- **用户级安装**：无需管理员权限，仅当前用户可用

## 📁 文件结构

安装后文件结构示例：
```
选定的安装路径/
├── ThoughtEcho.exe          # 主程序
├── flutter_windows.dll    # Flutter引擎
├── app.so                 # 应用逻辑
├── data/
│   ├── flutter_assets/    # 应用资源
│   └── icudtl.dat        # Unicode数据
└── windows/
    └── runner/
        └── resources/
            └── app_icon.ico  # 应用图标
```

## 🔄 更新影响

### 自动更新
- 无论安装在何处，自动更新都会保留原有安装路径
- 更新不会重置用户选择的路径

### 手动重装
- 如果用户卸载后重新安装，需要重新选择路径
- 卸载时会清理所选路径下的所有文件

## ⚠️ 注意事项

### 路径限制
- 避免使用包含中文或特殊字符的路径
- 路径长度建议不超过 100 个字符
- 确保目标磁盘有足够的空间（建议至少 200MB）

### 网络驱动器
- 不建议安装到网络驱动器，可能影响性能
- 如果必须安装到网络路径，确保网络稳定性

## 🛠️ 开发测试

### 本地测试
```bash
# 构建MSIX包
flutter pub run msix:create

# 安装测试
双击 ThoughtEcho-Setup.msix
```

### 验证功能
1. 运行安装程序
2. 确认出现"选择安装位置"步骤
3. 选择自定义路径进行安装
4. 验证应用能否正常启动

## 📋 配置文件变更

**文件**：`msix_config.yaml`

**关键修改**：
```yaml
# 图标统一
logo_path: "windows/runner/resources/app_icon.ico"
start_menu_icon: "windows/runner/resources/app_icon.ico"
tile_icon: "windows/runner/resources/app_icon.ico"

# 安装路径选择
install_location: "custom"  # 从 "auto" 改为 "custom"
```

## 🔧 后续优化建议

1. **默认路径推荐**：可以根据磁盘空间自动推荐最佳安装路径
2. **路径验证**：安装前验证路径有效性和权限
3. **迁移工具**：提供从旧版本到新安装路径的数据迁移功能

## 📞 用户支持

如果用户遇到安装路径相关问题：
1. 检查目标路径的写入权限
2. 确认磁盘空间充足
3. 避免使用系统保留路径
4. 尝试以管理员身份运行安装程序（系统级安装）